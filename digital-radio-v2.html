<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Radio v2</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Doto:wght@100..900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Doto', sans-serif;
        }

        .radio-container {
            width: 420px;
            height: 620px;
            background: linear-gradient(145deg, #f0f0f0, #d8d8d8);
            border-radius: 35px;
            box-shadow:
                25px 25px 50px rgba(0, 0, 0, 0.15),
                -25px -25px 50px rgba(255, 255, 255, 0.9),
                inset 2px 2px 4px rgba(255, 255, 255, 0.3),
                inset -2px -2px 4px rgba(0, 0, 0, 0.05);
            padding: 40px 35px 50px 35px;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .radio-container::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border-radius: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            pointer-events: none;
        }

        .display {
            width: 100%;
            height: 90px;
            background: linear-gradient(145deg, #0a0a0a, #1a1a1a);
            border-radius: 20px;
            margin-bottom: 35px;
            box-shadow:
                inset 10px 10px 20px rgba(0, 0, 0, 0.5),
                inset -10px -10px 20px rgba(255, 255, 255, 0.02),
                0 2px 4px rgba(0, 0, 0, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #00ff88;
            font-size: 20px;
            font-weight: 600;
            text-shadow: 0 0 15px #00ff88, 0 0 30px rgba(0, 255, 136, 0.3);
            position: relative;
            border: 2px solid #333;
        }

        .display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(45deg, transparent 49%, rgba(255, 255, 255, 0.02) 50%, transparent 51%),
                radial-gradient(ellipse at top left, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            border-radius: 18px;
            pointer-events: none;
        }

        .display::after {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
            border-radius: 1px;
        }

        .controls-section {
            margin-bottom: 25px;
        }

        .volume-section {
            margin-bottom: 35px;
        }

        .volume-label {
            font-size: 13px;
            color: #777;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 700;
            letter-spacing: 2px;
        }

        .volume-slider {
            width: 100%;
            height: 12px;
            background: linear-gradient(145deg, #c8c8c8, #e0e0e0);
            border-radius: 25px;
            outline: none;
            box-shadow:
                inset 6px 6px 12px rgba(0, 0, 0, 0.15),
                inset -6px -6px 12px rgba(255, 255, 255, 0.9),
                0 2px 4px rgba(0, 0, 0, 0.1);
            -webkit-appearance: none;
            appearance: none;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 28px;
            height: 28px;
            background: linear-gradient(145deg, #f0f0f0, #d8d8d8);
            border-radius: 50%;
            cursor: pointer;
            box-shadow:
                6px 6px 12px rgba(0, 0, 0, 0.2),
                -6px -6px 12px rgba(255, 255, 255, 0.9),
                inset 2px 2px 4px rgba(255, 255, 255, 0.3),
                inset -2px -2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.2s ease;
        }

        .volume-slider::-webkit-slider-thumb:hover {
            background: linear-gradient(145deg, #f5f5f5, #e0e0e0);
            transform: scale(1.05);
        }

        .button-group {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 25px;
        }

        .control-button {
            width: 70px;
            height: 70px;
            background: linear-gradient(145deg, #f0f0f0, #d8d8d8);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            color: #555;
            box-shadow:
                10px 10px 20px rgba(0, 0, 0, 0.15),
                -10px -10px 20px rgba(255, 255, 255, 0.9),
                inset 2px 2px 4px rgba(255, 255, 255, 0.3),
                inset -2px -2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .control-button::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            right: 3px;
            bottom: 3px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.1);
            pointer-events: none;
        }

        .control-button:hover {
            color: #333;
            background: linear-gradient(145deg, #f5f5f5, #e0e0e0);
            transform: translateY(-1px);
        }

        .control-button:active {
            box-shadow:
                inset 6px 6px 12px rgba(0, 0, 0, 0.2),
                inset -6px -6px 12px rgba(255, 255, 255, 0.9);
            transform: translateY(0);
        }

        .power-button {
            width: 90px;
            height: 90px;
            background: linear-gradient(145deg, #ff4444, #cc3333);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 28px;
            color: white;
            box-shadow:
                12px 12px 24px rgba(0, 0, 0, 0.25),
                -12px -12px 24px rgba(255, 255, 255, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.2),
                inset -2px -2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            margin-top: 25px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .power-button::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border-radius: 50%;
            border: 1px solid rgba(255, 255, 255, 0.1);
            pointer-events: none;
        }

        .power-button:hover {
            background: linear-gradient(145deg, #ff5555, #dd4444);
            transform: translateY(-2px);
            box-shadow:
                15px 15px 30px rgba(0, 0, 0, 0.3),
                -15px -15px 30px rgba(255, 255, 255, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.2),
                inset -2px -2px 4px rgba(0, 0, 0, 0.2);
        }

        .power-button:active {
            box-shadow:
                inset 8px 8px 16px rgba(0, 0, 0, 0.4),
                inset -8px -8px 16px rgba(255, 255, 255, 0.1);
            transform: translateY(0);
        }

        .section-label {
            font-size: 11px;
            color: #999;
            text-align: center;
            margin-bottom: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
    </style>
</head>
<body>
    <div class="radio-container">
        <div class="display">
            FM 101.5 MHz
        </div>

        <div class="volume-section">
            <div class="volume-label">VOLUME</div>
            <input type="range" class="volume-slider" min="0" max="100" value="50">
        </div>

        <div class="controls-section">
            <div class="section-label">Channel</div>
            <div class="button-group">
                <button class="control-button" title="Previous Channel">◀</button>
                <button class="control-button" title="Next Channel">▶</button>
            </div>
        </div>

        <div class="controls-section">
            <div class="section-label">Country</div>
            <div class="button-group">
                <button class="control-button" title="Previous Country">◀</button>
                <button class="control-button" title="Next Country">▶</button>
            </div>
        </div>

        <button class="power-button" title="Power">⏻</button>
    </div>
</body>
</html>
