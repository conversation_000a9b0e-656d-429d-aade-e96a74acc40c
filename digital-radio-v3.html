<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Neumorphic Digital Radio</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Doto font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Doto:wght@100..900&display=swap" rel="stylesheet">

    <style>
        :root {
            --bg: #e0e5ec;
            --shadow-light: #ffffff;
            --shadow-dark: #a3b1c6;
            --accent: #6c63ff;
            --text: #333;
        }

        body {
            background: var(--bg);
            font-family: 'Doto', sans-serif;
            height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .radio {
            background: var(--bg);
            width: 400px;
            height: 500px;
            border-radius: 40px;
            padding: 2rem;
            box-shadow: 20px 20px 60px var(--shadow-dark),
                -20px -20px 60px var(--shadow-light);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }

        .antenna {
            position: absolute;
            top: -80px;
            width: 4px;
            height: 80px;
            background: var(--shadow-dark);
            border-radius: 2px;
            left: 50%;
            transform: translateX(-50%);
        }

        .display {
            width: 80%;
            height: 60px;
            background: var(--bg);
            border-radius: 15px;
            box-shadow: inset 4px 4px 8px var(--shadow-dark),
                inset -4px -4px 8px var(--shadow-light);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--text);
        }

        .btn-group,
        .slider-group {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--bg);
            box-shadow: 8px 8px 16px var(--shadow-dark),
                -8px -8px 16px var(--shadow-light);
            border: none;
            cursor: pointer;
            transition: 0.2s ease;
        }

        .button:active {
            box-shadow: inset 4px 4px 8px var(--shadow-dark),
                inset -4px -4px 8px var(--shadow-light);
        }

        .slider {
            -webkit-appearance: none;
            width: 200px;
            height: 8px;
            border-radius: 5px;
            background: var(--bg);
            box-shadow: inset 4px 4px 8px var(--shadow-dark),
                inset -4px -4px 8px var(--shadow-light);
            outline: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--accent);
            cursor: pointer;
            box-shadow: 0 0 5px var(--shadow-dark);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--accent);
            cursor: pointer;
        }

        .power-button {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: var(--accent);
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            border: none;
            box-shadow: 8px 8px 16px var(--shadow-dark),
                -8px -8px 16px var(--shadow-light);
            transition: 0.2s ease;
            cursor: pointer;
        }

        .power-button:active {
            box-shadow: inset 4px 4px 8px var(--shadow-dark),
                inset -4px -4px 8px var(--shadow-light);
        }
    </style>
</head>

<body>
    <div class="radio">
        <div class="antenna"></div>
        <div class="display">Station: 101.1 FM</div>

        <div class="slider-group">
            <input type="range" min="0" max="100" value="50" class="slider" id="volume">
        </div>

        <div class="btn-group">
            <button class="button" title="Previous Channel">&#9664;</button>
            <button class="button" title="Next Channel">&#9654;</button>
        </div>

        <div class="btn-group">
            <button class="button" title="Previous Country">&#9664;</button>
            <button class="button" title="Next Country">&#9654;</button>
        </div>

        <button class="power-button" title="Power">⏻</button>
    </div>
</body>

</html>
