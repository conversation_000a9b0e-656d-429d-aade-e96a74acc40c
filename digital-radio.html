<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Radio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .radio-container {
            position: relative;
            width: 480px;
            height: 320px;
            background: linear-gradient(145deg, #e8e8e8, #d0d0d0, #b8b8b8);
            border-radius: 25px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.8),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
            padding: 35px;
            border: 3px solid #a0a0a0;
            overflow: visible;
        }

        .radio-container::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            background: linear-gradient(145deg, #f5f5f5, #e0e0e0);
            border-radius: 15px;
            z-index: -1;
        }

        .antenna {
            position: absolute;
            top: -60px;
            right: 60px;
            width: 6px;
            height: 120px;
            background: linear-gradient(to top, #4a4a4a, #888, #bbb, #ddd);
            border-radius: 3px;
            box-shadow:
                2px 0 6px rgba(0, 0, 0, 0.4),
                inset 1px 0 1px rgba(255, 255, 255, 0.3);
            transform-origin: bottom center;
            animation: antennaGlow 3s ease-in-out infinite alternate;
        }

        .antenna::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -4px;
            width: 14px;
            height: 14px;
            background: radial-gradient(circle, #ff6b35, #ff4500, #cc3300);
            border-radius: 50%;
            box-shadow:
                0 0 15px rgba(255, 107, 53, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.3);
        }

        .antenna::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: -2px;
            width: 10px;
            height: 8px;
            background: linear-gradient(145deg, #666, #333);
            border-radius: 2px;
        }

        @keyframes antennaGlow {
            0% { box-shadow: 2px 0 6px rgba(0, 0, 0, 0.4), inset 1px 0 1px rgba(255, 255, 255, 0.3); }
            100% { box-shadow: 2px 0 6px rgba(0, 0, 0, 0.4), inset 1px 0 1px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 107, 53, 0.3); }
        }

        .display {
            width: 100%;
            height: 80px;
            background: linear-gradient(145deg, #000, #111, #000);
            border-radius: 12px;
            border: 4px solid #333;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 35px;
            position: relative;
            overflow: hidden;
            padding: 0 20px;
            box-shadow:
                inset 0 0 20px rgba(0, 0, 0, 0.8),
                0 2px 10px rgba(0, 0, 0, 0.5);
        }

        .display::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(45deg, transparent 48%, rgba(0, 255, 0, 0.05) 50%, transparent 52%),
                radial-gradient(ellipse at center, transparent 60%, rgba(0, 0, 0, 0.3) 100%);
            pointer-events: none;
        }

        .display-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .display-text {
            color: #00ff41;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 0 10px #00ff41;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }

        .frequency-display {
            color: #ff6b35;
            font-family: 'Courier New', monospace;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 0 0 15px #ff6b35;
            letter-spacing: 2px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
            align-items: flex-end;
        }

        .signal-bar {
            width: 4px;
            background: #00ff41;
            border-radius: 1px;
            box-shadow: 0 0 5px #00ff41;
        }

        .signal-bar:nth-child(1) { height: 8px; }
        .signal-bar:nth-child(2) { height: 12px; }
        .signal-bar:nth-child(3) { height: 16px; }
        .signal-bar:nth-child(4) { height: 20px; }
        .signal-bar:nth-child(5) { height: 24px; }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            align-items: center;
            margin-bottom: 20px;
        }

        .volume-knob {
            width: 70px;
            height: 70px;
            background:
                radial-gradient(circle at 30% 30%, #f0f0f0, #d0d0d0, #a0a0a0, #707070);
            border-radius: 50%;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 4px solid #888;
            box-shadow:
                0 8px 20px rgba(0, 0, 0, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.8),
                inset 0 -2px 4px rgba(0, 0, 0, 0.3);
        }

        .volume-knob:hover {
            transform: scale(1.05) translateY(-2px);
            box-shadow:
                0 12px 25px rgba(0, 0, 0, 0.4),
                inset 0 2px 4px rgba(255, 255, 255, 0.9),
                inset 0 -2px 4px rgba(0, 0, 0, 0.4);
        }

        .volume-knob::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 25px;
            background: linear-gradient(to bottom, #ff6b35, #ff4500);
            border-radius: 3px;
            box-shadow:
                0 0 8px rgba(255, 107, 53, 0.8),
                inset 1px 0 1px rgba(255, 255, 255, 0.5);
        }

        .volume-knob::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, #333, #111);
            border-radius: 50%;
            border: 2px solid #555;
        }

        .button {
            width: 55px;
            height: 55px;
            background: linear-gradient(145deg, #e8e8e8, #d0d0d0, #b8b8b8);
            border-radius: 50%;
            border: 3px solid #999;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow:
                0 6px 18px rgba(0, 0, 0, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.8),
                inset 0 -2px 4px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 20px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
        }

        .button:hover {
            transform: translateY(-3px);
            box-shadow:
                0 10px 25px rgba(0, 0, 0, 0.4),
                inset 0 2px 4px rgba(255, 255, 255, 0.9),
                inset 0 -2px 4px rgba(0, 0, 0, 0.3);
        }

        .button:active {
            transform: translateY(1px);
            box-shadow:
                0 3px 10px rgba(0, 0, 0, 0.3),
                inset 0 3px 6px rgba(0, 0, 0, 0.4);
        }

        .power-button {
            background: linear-gradient(145deg, #ff4757, #c44569, #a73e47);
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            border-color: #a73e47;
        }

        .power-button.on {
            background: linear-gradient(145deg, #2ed573, #1e90ff, #0066cc);
            border-color: #0066cc;
            box-shadow:
                0 6px 18px rgba(0, 0, 0, 0.3),
                0 0 25px rgba(46, 213, 115, 0.6),
                inset 0 2px 4px rgba(255, 255, 255, 0.3);
        }

        .speaker-grille {
            width: 100%;
            height: 60px;
            background: linear-gradient(145deg, #2a2a2a, #1a1a1a, #0a0a0a);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            border: 3px inset #444;
            box-shadow:
                inset 0 0 15px rgba(0, 0, 0, 0.8),
                0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .speaker-holes {
            display: grid;
            grid-template-columns: repeat(25, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 3px;
            padding: 8px;
            height: 100%;
        }

        .speaker-hole {
            background: radial-gradient(circle, #000, #111);
            border-radius: 50%;
            box-shadow:
                inset 0 2px 4px rgba(0, 0, 0, 0.9),
                0 1px 1px rgba(255, 255, 255, 0.1);
        }

        .brand-label {
            position: absolute;
            bottom: 15px;
            left: 35px;
            color: #666;
            font-size: 14px;
            font-weight: bold;
            letter-spacing: 2px;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
        }

        .radio-model {
            position: absolute;
            top: 15px;
            left: 35px;
            color: #666;
            font-size: 12px;
            font-weight: bold;
            letter-spacing: 1px;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="radio-container">
        <div class="antenna"></div>

        <div class="radio-model">DX-2000</div>

        <div class="display">
            <div class="display-left">
                <div class="display-text" id="displayText">CLASSIC ROCK</div>
                <div class="frequency-display" id="frequencyDisplay">101.5</div>
            </div>
            <div class="signal-bars">
                <div class="signal-bar"></div>
                <div class="signal-bar"></div>
                <div class="signal-bar"></div>
                <div class="signal-bar"></div>
                <div class="signal-bar"></div>
            </div>
        </div>

        <div class="controls">
            <div class="volume-knob" id="volumeKnob" title="Volume"></div>
            <button class="button" id="prevBtn" title="Previous Channel">‹</button>
            <button class="button" id="nextBtn" title="Next Channel">›</button>
            <button class="button power-button" id="powerBtn" title="Power">⏻</button>
        </div>

        <div class="speaker-grille">
            <div class="speaker-holes">
                <!-- Speaker holes will be generated by JavaScript -->
            </div>
        </div>

        <div class="brand-label">DIGITAL RADIO</div>
    </div>

    <script>
        // Generate speaker holes
        const speakerHoles = document.querySelector('.speaker-holes');
        for (let i = 0; i < 100; i++) {
            const hole = document.createElement('div');
            hole.className = 'speaker-hole';
            speakerHoles.appendChild(hole);
        }

        // Radio functionality
        let isOn = false;
        let currentStation = 0;
        let volume = 50;

        const stations = [
            { name: "CLASSIC ROCK", freq: "101.5" },
            { name: "JAZZ LOUNGE", freq: "102.3" },
            { name: "POP HITS", freq: "103.7" },
            { name: "NEWS TALK", freq: "104.1" },
            { name: "ELECTRONIC", freq: "105.9" }
        ];

        const displayText = document.getElementById('displayText');
        const frequencyDisplay = document.getElementById('frequencyDisplay');
        const powerBtn = document.getElementById('powerBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const volumeKnob = document.getElementById('volumeKnob');

        function updateDisplay() {
            if (isOn) {
                displayText.textContent = stations[currentStation].name;
                frequencyDisplay.textContent = stations[currentStation].freq;
            } else {
                displayText.textContent = "OFF";
                frequencyDisplay.textContent = "---";
            }
        }

        powerBtn.addEventListener('click', () => {
            isOn = !isOn;
            powerBtn.classList.toggle('on', isOn);
            updateDisplay();
        });

        prevBtn.addEventListener('click', () => {
            if (isOn) {
                currentStation = (currentStation - 1 + stations.length) % stations.length;
                updateDisplay();
            }
        });

        nextBtn.addEventListener('click', () => {
            if (isOn) {
                currentStation = (currentStation + 1) % stations.length;
                updateDisplay();
            }
        });

        volumeKnob.addEventListener('click', (e) => {
            if (isOn) {
                volume = Math.min(100, volume + 10);
                if (volume > 100) volume = 0;

                // Rotate the knob indicator
                const rotation = (volume / 100) * 270 - 135;
                volumeKnob.style.transform = `rotate(${rotation}deg)`;
            }
        });

        // Initialize display
        updateDisplay();
    </script>
</body>
</html>
